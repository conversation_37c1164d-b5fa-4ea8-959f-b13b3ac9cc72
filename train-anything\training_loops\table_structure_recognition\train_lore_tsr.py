#!/usr/bin/env python3
"""
LORE-TSR 表格结构识别训练脚本

基于 train-anything 框架的 accelerate 分布式训练实现

迭代1：训练入口空框架
后续迭代将逐步实现具体功能

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: LORE-TSR训练入口，基于train-anything框架的accelerate分布式训练实现

使用示例:
    # 基本训练
    python training_loops/table_structure_recognition/train_lore_tsr.py \
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml

    # 调试模式
    python training_loops/table_structure_recognition/train_lore_tsr.py \
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
        --debug

    # 干运行模式（只可视化数据，不训练）
    python training_loops/table_structure_recognition/train_lore_tsr.py \
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
        --dry-run
"""

import os
import math
import json
import random
import datetime
import warnings
from pathlib import Path
from itertools import islice
from typing import Tuple, List

# 设置环境变量解决DDP未使用参数问题 - 必须在导入torch之前设置
os.environ['TORCH_DISTRIBUTED_DEBUG'] = 'DETAIL'

import torch
import torch.nn as nn
import torch.utils.data
from tqdm import tqdm

from accelerate.logging import get_logger
import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from modules.utils.torch_utils import EMAHandler
from modules.utils.optimization import get_scheduler
from modules.proj_cmd_args.lore_tsr.args import parse_args
from modules.utils.train_utils import prepare_training_enviornment_v2
from modules.utils.train_tools import (
    save_state,
    get_optimizer,
    get_random_states,
    set_random_states,
    walk_dataloaders,
    find_latest_checkpoint,
)

# LORE-TSR组件导入
# 迭代2：模型相关导入
from networks.lore_tsr import create_lore_tsr_model
# 迭代4步骤4.3：完整损失函数导入
from networks.lore_tsr.lore_tsr_loss import LoreTsrBasicLoss, LoreTsrLoss
# 迭代6步骤6.2：真实Processor导入
from networks.lore_tsr.processor import Processor
# 迭代4步骤4.3：DummyProcessor导入（保留作为备用）
from modules.utils.lore_tsr.dummy_processor import DummyProcessor
# 迭代3：数据集相关导入
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from my_datasets.table_structure_recognition.lore_tsr_transforms import get_lore_tsr_transforms

# 使用新的配置系统
config = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(config.basic.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning)


def create_model_and_ema(config, accelerator, model_state_dict, ema_path, weight_dtype, load_state_dict_msg):
    """
    创建LORE-TSR模型实例和EMA包装器

    基于train-anything框架标准，参考cycle-centernet-ms实现模式

    Args:
        config: 配置对象
        accelerator: accelerate对象
        model_state_dict: 模型状态字典
        ema_path: EMA模型路径
        weight_dtype: 权重数据类型
        load_state_dict_msg: 加载状态消息

    Returns:
        model: 模型实例
        ema_handler: EMA处理器
    """
    logger.info("创建LORE-TSR模型和EMA（迭代2步骤2.4）")

    # 使用模型工厂函数创建模型
    from networks.lore_tsr import create_lore_tsr_model
    model = create_lore_tsr_model(config)

    # 设置模型数据类型
    if weight_dtype != torch.float32:
        logger.info(f"设置模型权重类型为: {weight_dtype}")
        model = model.to(dtype=weight_dtype)

    # 加载预训练权重
    if model_state_dict is not None:
        try:
            # 使用strict=False允许部分权重加载
            missing_keys, unexpected_keys = model.load_state_dict(model_state_dict, strict=False)

            if missing_keys:
                logger.warning(f"模型权重加载 - 缺失键: {len(missing_keys)} 个")
                file_logger.warning(f"缺失的权重键: {missing_keys}")

            if unexpected_keys:
                logger.warning(f"模型权重加载 - 意外键: {len(unexpected_keys)} 个")
                file_logger.warning(f"意外的权重键: {unexpected_keys}")

            logger.info(f"模型权重加载完成: {load_state_dict_msg}")

        except Exception as e:
            logger.error(f"模型权重加载失败: {e}")
            file_logger.error(f"权重加载错误详情: {e}")
            raise e

    # 创建EMA处理器
    ema_handler = None
    if config.ema.enabled:
        try:
            ema_handler = EMAHandler(model, config.ema.decay)

            # 加载EMA权重
            if ema_path and os.path.exists(ema_path):
                ema_state_dict = torch.load(ema_path, map_location='cpu')
                ema_handler.load_state_dict(ema_state_dict)
                logger.info(f"EMA权重加载完成: {ema_path}")

            logger.info(f"EMA处理器创建完成，衰减率: {config.ema.decay}")

        except Exception as e:
            logger.error(f"EMA处理器创建失败: {e}")
            file_logger.error(f"EMA创建错误详情: {e}")
            # EMA失败不应该阻止训练，设置为None继续
            ema_handler = None
            logger.warning("EMA处理器创建失败，将继续进行无EMA训练")
    else:
        logger.info("EMA功能未启用")

    # 获取并记录模型信息
    try:
        model_info = model.get_model_info()
        logger.info(f"模型创建完成 - {model_info['model_name']}")
        logger.info(f"架构: {model_info['arch_name']}, 类别数: {model_info['num_classes']}")
        logger.info(f"总参数: {model_info['total_params']:,}, 可训练: {model_info['trainable_params']:,}")
        logger.info(f"模型大小: {model_info['model_size_mb']:.2f} MB")

        file_logger.info(f"模型详细信息: {model_info}")

    except Exception as e:
        logger.warning(f"获取模型信息失败: {e}")
        file_logger.warning(f"模型信息获取错误: {e}")

    return model, ema_handler


def prepare_model_for_training(config, model, accelerator, weight_dtype):
    """
    准备模型进行训练，包括验证和配置检查

    Args:
        config: 配置对象
        model: 模型实例
        accelerator: accelerate对象
        weight_dtype: 权重数据类型

    Returns:
        model: 准备好的模型实例
    """
    logger.info("准备模型进行训练...")

    # 验证检测头配置
    from networks.lore_tsr.heads import validate_heads_config, print_heads_summary

    if validate_heads_config(config.model.heads):
        logger.info("✅ 检测头配置验证通过")

        # 打印检测头配置摘要
        logger.info("检测头配置摘要:")
        print_heads_summary(dict(config.model.heads))

    else:
        logger.error("❌ 检测头配置验证失败")
        raise ValueError("检测头配置无效")

    # 设置模型为训练模式
    model.train()

    # 验证模型前向传播
    try:
        logger.info("验证模型前向传播...")

        # 创建测试输入
        test_input = torch.randn(
            1, 3,
            config.data.input_h,
            config.data.input_w,
            dtype=weight_dtype,
            device=accelerator.device
        )

        # 执行前向传播
        with torch.no_grad():
            outputs = model(test_input)

        # 验证输出格式
        if isinstance(outputs, list) and len(outputs) > 0:
            output_dict = outputs[0]
            if isinstance(output_dict, dict):
                logger.info(f"✅ 前向传播验证成功，输出检测头: {list(output_dict.keys())}")

                # 记录输出形状
                for head, tensor in output_dict.items():
                    logger.info(f"  {head}: {tensor.shape}")

            else:
                logger.warning("⚠️ 输出格式异常：期望字典类型")
        else:
            logger.warning("⚠️ 输出格式异常：期望列表类型")

    except Exception as e:
        logger.error(f"❌ 模型前向传播验证失败: {e}")
        file_logger.error(f"前向传播验证错误详情: {e}")
        raise e

    # 使用accelerator准备模型
    model = accelerator.prepare(model)
    logger.info("✅ 模型已通过accelerator准备")

    return model


def setup_training_components(config, model, optimizer_ckpt, lr_scheduler_ckpt, accelerator):
    """
    设置优化器、学习率调度器和损失函数

    迭代6步骤6.2：集成完整损失函数和真实Processor

    Args:
        config: 配置对象
        model: 模型实例
        optimizer_ckpt: 优化器检查点
        lr_scheduler_ckpt: 学习率调度器检查点
        accelerator: accelerate对象

    Returns:
        loss_criterion: 损失函数
        processor: 处理器（真实Processor）
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        max_train_steps: 最大训练步数
        train_datasets: 训练数据集列表
        train_loaders: 训练数据加载器列表
        val_loaders: 验证数据加载器列表
        seed: 随机种子
    """
    logger.info("设置LORE-TSR训练组件（迭代4步骤4.3：完整损失函数集成）")

    # 创建LORE-TSR损失函数（支持基础版本和完整版本）
    use_full_loss = config.loss.get('use_full_loss', True)  # 默认使用完整损失函数

    if use_full_loss:
        loss_criterion = LoreTsrLoss(config)
        logger.info("✅ LORE-TSR完整损失函数创建成功")
        logger.info(f"   - wiz_pairloss: {config.loss.get('wiz_pairloss', False)}")
        logger.info(f"   - wiz_stacking: {config.loss.get('wiz_stacking', False)}")
        logger.info(f"   - ax_weight: {config.loss.weights.get('ax_weight', 2.0)}")
    else:
        loss_criterion = LoreTsrBasicLoss(config)
        logger.info("✅ LORE-TSR基础损失函数创建成功（兼容模式）")

    # 创建真实Processor（迭代6步骤6.2）
    processor = Processor(config)
    logger.info("✅ 真实Processor组件创建成功")

    # 移动Processor到正确的设备
    processor = processor.to(accelerator.device)
    logger.info(f"✅ Processor已移动到设备: {accelerator.device}")

    # 创建优化器
    optimizer = get_optimizer(model, config)
    if optimizer_ckpt is not None:
        optimizer.load_state_dict(optimizer_ckpt)
        logger.info("✅ 优化器状态已恢复")

    # 创建学习率调度器
    lr_scheduler = get_scheduler(optimizer, config)
    if lr_scheduler_ckpt is not None:
        lr_scheduler.load_state_dict(lr_scheduler_ckpt)
        logger.info("✅ 学习率调度器状态已恢复")

    # 准备数据加载器
    train_datasets, train_loaders = prepare_dataloaders(config, "train", config.training.batch_size)
    val_datasets, val_loaders = prepare_dataloaders(config, "val", config.training.batch_size)

    # 计算最大训练步数
    max_train_steps = config.training.epochs * len(train_loaders[0][1]) if train_loaders else config.training.epochs * 100

    seed = config.basic.seed

    return loss_criterion, processor, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed


def prepare_dataloaders(config, mode: str, batch_size_per_device: int, seed: int = -1):
    """
    准备数据加载器

    迭代3：实现基础数据加载器

    Args:
        config: 配置对象
        mode: 模式 ('train', 'val')
        batch_size_per_device: 每设备批次大小
        seed: 随机种子

    Returns:
        datasets: 数据集列表
        loaders: 数据加载器列表
    """
    logger.info(f"准备{mode}数据加载器（迭代3：基础实现）")

    # 创建数据集
    dataset = LoreTsrDataset(config, mode=mode)

    # 创建数据变换
    transforms = get_lore_tsr_transforms(config, mode=mode)
    dataset.transforms = transforms

    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size_per_device,
        shuffle=(mode == 'train'),
        num_workers=config.data.loader.num_workers,
        pin_memory=config.data.loader.pin_memory
    )

    datasets = [dataset]
    loaders = [(f"lore_tsr_{mode}", dataloader)]

    logger.info(f"✅ {mode}数据加载器创建成功，数据集大小: {len(dataset)}")

    return datasets, loaders


def load_checkpoint_state(config, accelerator, logger, file_logger):
    """
    加载检查点状态
    
    迭代1：空实现占位
    迭代3：实现检查点加载逻辑
    """
    logger.info("加载检查点状态（迭代1：空实现占位）")
    file_logger.info("等待迭代3实现具体的检查点加载逻辑")
    
    # 迭代1：返回默认值
    return 0, 0, None, None, None, None, "迭代1：空实现占位"


def handle_dry_run_mode(config, model, accelerator, weight_dtype, train_loaders):
    """
    处理干运行模式
    
    迭代1：基础实现
    """
    if config.debug_visualization.dry_run:
        logger.info("启用干运行模式：只可视化数据，不进行训练")
        file_logger.info("干运行模式：等待迭代5实现数据可视化逻辑")
        return True
    return False


def initialize_best_model_record(best_loss_model_record):
    """
    初始化最佳模型记录
    
    迭代1：基础实现
    """
    logger.info("初始化最佳模型记录")
    return {"best_loss": float('inf'), "best_epoch": 0}


def prepare_accelerator_components(accelerator, model, optimizer, lr_scheduler, train_loaders, val_loaders):
    """
    准备accelerator组件
    
    迭代1：空实现占位
    迭代3：实现accelerator组件准备
    """
    logger.info("准备accelerator组件（迭代1：空实现占位）")
    file_logger.info("等待迭代3实现具体的accelerator组件准备逻辑")
    
    # 迭代1：直接返回输入参数
    return model, optimizer, lr_scheduler, train_loaders, val_loaders


def handle_visualization_only_mode(config, model, accelerator, weight_dtype, start_steps):
    """
    处理只可视化模式
    
    迭代1：基础实现
    """
    if config.basic.only_vis_log:
        logger.info("启用只可视化模式")
        file_logger.info("只可视化模式：等待后续迭代实现可视化逻辑")
        return True
    return False


def calculate_final_training_steps(config, accelerator, train_loaders, max_train_steps, train_datasets, seed):
    """
    计算最终训练步数
    
    迭代1：基础实现
    """
    logger.info("计算最终训练步数（迭代1：基础实现）")
    
    # 迭代1：基于配置计算基础步数
    if max_train_steps is None:
        # 假设每个epoch有100步（实际将在迭代5根据数据集大小计算）
        max_train_steps = config.training.epochs * 100
    
    logger.info(f"预计最大训练步数: {max_train_steps}")
    return max_train_steps


def run_training_loop(config, model, accelerator, ema_handler, loss_criterion, processor,
                     optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
                     global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data):
    """
    执行完整的训练循环

    迭代6步骤6.2：集成真实processor参数

    Args:
        config: 配置对象
        model: 模型实例
        accelerator: accelerate对象
        ema_handler: EMA处理器
        loss_criterion: 损失函数
        processor: 处理器（真实Processor）
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        weight_dtype: 权重数据类型
        train_loaders: 训练数据加载器列表
        val_loaders: 验证数据加载器列表
        global_step: 全局步数
        first_epoch: 起始epoch
        max_train_steps: 最大训练步数
        best_loss_model_record: 最佳模型记录路径
        best_loss_record_data: 最佳模型记录数据

    Returns:
        global_step: 更新后的全局步数
    """
    logger.info("开始LORE-TSR训练循环（迭代3步骤3.2：增强版）")

    # 训练指标记录
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []

    for epoch in range(first_epoch, config.training.epochs):
        logger.info(f"训练 Epoch {epoch + 1}/{config.training.epochs}")

        # ===== 训练阶段 =====
        model.train()
        epoch_train_loss = 0.0
        epoch_train_stats = {}
        num_train_batches = 0

        for loader_name, train_loader in train_loaders:
            for batch_idx, batch in enumerate(train_loader):
                try:
                    # 前向传播
                    with accelerator.autocast():
                        predictions = model(batch['input'])

                        # 处理模型输出格式
                        if isinstance(predictions, list) and len(predictions) > 0:
                            predictions = predictions[0]  # 取第一个输出

                        # 新增：调用Processor进行逻辑结构恢复
                        logic_axis = None
                        stacked_axis = None
                        if config.processor.wiz_stacking:
                            logic_axis, stacked_axis = processor(predictions, batch['targets'])
                        else:
                            logic_axis = processor(predictions, batch['targets'])

                        # 将logic_axis传递给损失函数
                        total_loss, loss_stats = loss_criterion(predictions, batch['targets'], logic_axis)

                    # 反向传播
                    accelerator.backward(total_loss)

                    # 梯度裁剪（可选）
                    if hasattr(config.training, 'max_grad_norm') and config.training.max_grad_norm:
                        accelerator.clip_grad_norm_(model.parameters(), config.training.max_grad_norm)

                    optimizer.step()
                    optimizer.zero_grad()

                    # 更新EMA
                    if ema_handler is not None:
                        ema_handler.update(model)

                    # 累积损失统计
                    epoch_train_loss += total_loss.item()
                    for key, value in loss_stats.items():
                        if key not in epoch_train_stats:
                            epoch_train_stats[key] = 0.0
                        epoch_train_stats[key] += value

                    num_train_batches += 1
                    global_step += 1

                    # 定期记录训练指标
                    if global_step % 50 == 0:
                        current_lr = optimizer.param_groups[0]['lr']
                        logger.info(f"Step {global_step}, Loss: {total_loss.item():.4f}, LR: {current_lr:.6f}")

                        # 记录到accelerator tracker
                        if accelerator.is_main_process:
                            accelerator.log({
                                "train/loss": total_loss.item(),
                                "train/learning_rate": current_lr,
                                "train/global_step": global_step
                            }, step=global_step)

                    # 检查是否达到最大步数
                    if global_step >= max_train_steps:
                        break

                except Exception as e:
                    logger.warning(f"训练步骤失败，跳过: {e}")
                    continue

            if global_step >= max_train_steps:
                break

        # 计算训练平均损失
        avg_train_loss = epoch_train_loss / max(num_train_batches, 1)
        train_losses.append(avg_train_loss)

        # 计算平均训练统计
        avg_train_stats = {}
        for key, value in epoch_train_stats.items():
            avg_train_stats[key] = value / max(num_train_batches, 1)

        logger.info(f"Epoch {epoch + 1} 训练平均损失: {avg_train_loss:.4f}")

        # ===== 验证阶段 =====
        val_freq = getattr(config.training, 'val_freq', 1)
        if val_loaders and (epoch + 1) % val_freq == 0:
            val_loss, val_stats = validate_one_epoch(
                model, val_loaders, loss_criterion, processor, config, accelerator, weight_dtype
            )
            val_losses.append(val_loss)

            logger.info(f"Epoch {epoch + 1} 验证平均损失: {val_loss:.4f}")

            # 记录验证指标
            if accelerator.is_main_process:
                log_data = {
                    "val/loss": val_loss,
                    "train/epoch_loss": avg_train_loss,
                    "train/epoch": epoch + 1
                }
                log_data.update({f"val/{k}": v for k, v in val_stats.items()})
                log_data.update({f"train/{k}": v for k, v in avg_train_stats.items()})
                accelerator.log(log_data, step=global_step)

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                logger.info(f"发现更好的模型，验证损失: {val_loss:.4f}")

                if accelerator.is_main_process:
                    save_best_model(
                        config, model, optimizer, lr_scheduler, accelerator,
                        ema_handler, global_step, val_loss, best_loss_model_record
                    )

        # 学习率调度
        lr_scheduler.step()

        # 定期保存检查点
        save_freq = getattr(config.training, 'save_freq', 10)
        if (epoch + 1) % save_freq == 0:
            if accelerator.is_main_process:
                save_checkpoint(
                    config, model, optimizer, lr_scheduler, accelerator,
                    ema_handler, global_step, epoch + 1
                )

        if global_step >= max_train_steps:
            break

    logger.info(f"训练循环完成，最终步数: {global_step}")
    logger.info(f"最佳验证损失: {best_val_loss:.4f}")

    return global_step


def validate_one_epoch(model, val_loaders, loss_criterion, processor, config, accelerator, weight_dtype):
    """执行一个验证epoch"""
    model.eval()
    total_val_loss = 0.0
    total_val_stats = {}
    num_val_batches = 0

    with torch.no_grad():
        for loader_name, val_loader in val_loaders:
            for batch in val_loader:
                try:
                    with accelerator.autocast():
                        predictions = model(batch['input'])

                        # 处理模型输出格式
                        if isinstance(predictions, list) and len(predictions) > 0:
                            predictions = predictions[0]  # 取第一个输出

                        # 验证模式的Processor调用
                        logic_axis = None
                        stacked_axis = None
                        if config.processor.wiz_stacking:
                            logic_axis, stacked_axis = processor(predictions, batch['targets'])
                        else:
                            logic_axis = processor(predictions, batch['targets'])

                        val_loss, val_stats = loss_criterion(predictions, batch['targets'], logic_axis)

                    total_val_loss += val_loss.item()
                    for key, value in val_stats.items():
                        if key not in total_val_stats:
                            total_val_stats[key] = 0.0
                        total_val_stats[key] += value

                    num_val_batches += 1

                except Exception as e:
                    logger.warning(f"验证步骤失败，跳过: {e}")
                    continue

    # 计算平均值
    avg_val_loss = total_val_loss / max(num_val_batches, 1)
    avg_val_stats = {}
    for key, value in total_val_stats.items():
        avg_val_stats[key] = value / max(num_val_batches, 1)

    return avg_val_loss, avg_val_stats


def save_best_model(config, model, optimizer, lr_scheduler, accelerator,
                   ema_handler, global_step, val_loss, best_loss_model_record):
    """保存最佳模型"""
    try:
        save_path = os.path.join(config.basic.output_dir, "best_model")
        os.makedirs(save_path, exist_ok=True)

        # 保存模型状态
        accelerator.save_state(save_path)

        # 更新最佳模型记录
        record_data = {
            "global_step": global_step,
            "val_loss": val_loss,
            "save_path": save_path,
            "timestamp": datetime.datetime.now().isoformat()
        }

        with open(best_loss_model_record, 'w') as f:
            json.dump(record_data, f, indent=2)

        logger.info(f"最佳模型已保存到: {save_path}")

    except Exception as e:
        logger.error(f"保存最佳模型失败: {e}")


def save_checkpoint(config, model, optimizer, lr_scheduler, accelerator,
                   ema_handler, global_step, epoch):
    """保存训练检查点"""
    try:
        checkpoint_path = os.path.join(config.basic.output_dir, f"checkpoint_epoch_{epoch}")
        os.makedirs(checkpoint_path, exist_ok=True)

        accelerator.save_state(checkpoint_path)

        logger.info(f"检查点已保存到: {checkpoint_path}")

    except Exception as e:
        logger.error(f"保存检查点失败: {e}")


def save_final_model(config, model, optimizer, lr_scheduler, accelerator, ema_handler, global_step):
    """
    保存最终模型

    迭代3步骤3.2：实现完整的模型保存逻辑
    """
    logger.info("保存最终模型（迭代3步骤3.2：完整实现）")

    accelerator.wait_for_everyone()

    if accelerator.is_main_process:
        try:
            # 保存最终模型
            final_save_path = os.path.join(config.basic.output_dir, "final_model")
            os.makedirs(final_save_path, exist_ok=True)

            accelerator.save_state(final_save_path)

            # 保存模型信息
            model_info = {
                "global_step": global_step,
                "model_arch": config.model.arch_name,
                "num_classes": config.model.heads.hm,
                "input_size": config.data.processing.image_size,
                "training_completed": True,
                "save_timestamp": datetime.datetime.now().isoformat()
            }

            info_path = os.path.join(final_save_path, "model_info.json")
            with open(info_path, 'w') as f:
                json.dump(model_info, f, indent=2)

            logger.info(f"最终模型已保存到: {final_save_path}")
            logger.info(f"模型信息已保存到: {info_path}")

        except Exception as e:
            logger.error(f"保存最终模型失败: {e}")


def main():
    """LORE-TSR训练主入口函数"""
    global config
    if config is None:
        config = parse_args()

    logger.info("=" * 80)
    logger.info("LORE-TSR 表格结构识别训练开始")
    logger.info("=" * 80)
    logger.info(f"迭代2步骤2.4：完整模型创建和初始化")
    logger.info(f"任务类型: {config.basic.task}")
    logger.info(f"模型架构: {config.model.arch_name}")
    logger.info(f"训练轮次: {config.training.epochs}")
    logger.info(f"批次大小: {config.training.batch_size}")
    logger.info(f"输出目录: {config.basic.output_dir}")
    logger.info(f"输入尺寸: {config.data.input_h}x{config.data.input_w}")
    logger.info(f"EMA启用: {config.ema.enabled}")

    # 准备训练环境
    logger.info("准备训练环境...")
    accelerator, weight_dtype = prepare_training_enviornment_v2(config, logger)
    logger.info(f"accelerate环境初始化成功，设备: {accelerator.device}")

    # 初始化最佳模型记录
    best_loss_model_record = os.path.join(config.basic.output_dir, "best_loss_model", "record.json")
    os.makedirs(Path(best_loss_model_record).parent, exist_ok=True)

    # 加载检查点状态
    logger.info("加载检查点状态...")
    start_steps, start_epoch, ema_path, model_state_dict, optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg = \
        load_checkpoint_state(config, accelerator, logger, file_logger)

    # 创建模型和EMA处理器
    logger.info("创建模型和EMA处理器...")
    model, ema_handler = create_model_and_ema(
        config, accelerator, model_state_dict, ema_path, weight_dtype, load_state_dict_msg
    )

    # 准备模型进行训练
    logger.info("准备模型进行训练...")
    model = prepare_model_for_training(config, model, accelerator, weight_dtype)

    # 设置训练组件
    logger.info("设置训练组件...")
    loss_criterion, processor, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed = \
        setup_training_components(config, model, optimizer_ckpt, lr_scheduler_ckpt, accelerator)

    # 处理干运行模式
    if handle_dry_run_mode(config, model, accelerator, weight_dtype, train_loaders):
        logger.info("干运行模式完成，退出程序")
        return

    # 初始化最佳模型记录
    best_loss_record_data = initialize_best_model_record(best_loss_model_record)

    # 准备accelerator组件
    logger.info("准备accelerator组件...")
    model, optimizer, lr_scheduler, train_loaders, val_loaders = prepare_accelerator_components(
        accelerator, model, optimizer, lr_scheduler, train_loaders, val_loaders
    )

    # 初始化训练跟踪器
    if accelerator.is_main_process:
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{config.distributed.tracker_project_name}-{exp_date}")
        logger.info(f"训练跟踪器初始化完成: {config.distributed.tracker_project_name}-{exp_date}")

    # 处理只可视化模式
    if handle_visualization_only_mode(config, model, accelerator, weight_dtype, start_steps):
        logger.info("只可视化模式完成，退出程序")
        accelerator.end_training()
        return

    # 计算最终训练步数并记录训练信息
    max_train_steps = calculate_final_training_steps(
        config, accelerator, train_loaders, max_train_steps, train_datasets, seed
    )

    # 设置起始epoch和global_step
    global_step = start_steps
    first_epoch = start_epoch

    logger.info("开始训练循环...")
    logger.info(f"起始epoch: {first_epoch}")
    logger.info(f"起始步数: {global_step}")
    logger.info(f"最大训练步数: {max_train_steps}")

    # 运行训练循环
    global_step = run_training_loop(
        config, model, accelerator, ema_handler, loss_criterion, processor,
        optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
        global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data
    )

    # 训练结束，保存最终模型
    logger.info("训练完成，保存最终模型...")
    save_final_model(config, model, optimizer, lr_scheduler, accelerator, ema_handler, global_step)

    logger.info("=" * 80)
    logger.info("LORE-TSR训练框架初始化完成")
    logger.info("迭代2步骤2.4执行成功")
    logger.info("✅ 模型创建和初始化逻辑完整实现")
    logger.info("✅ 检测头配置验证和前向传播测试通过")
    logger.info("✅ accelerator集成和EMA处理器配置完成")
    logger.info("等待迭代3实现训练循环和损失函数...")
    logger.info("=" * 80)

    accelerator.end_training()


if __name__ == '__main__':
    main()
