#!/usr/bin/env python3
"""
LORE-TSR 工具模块

迭代4步骤4.2：创建基础模块结构
迭代5步骤5.1：添加基础图像工具函数
迭代6：将添加完整的Processor和Transformer实现
"""

__version__ = "0.1.0"
__author__ = "LORE-TSR Migration Team"

# 当前可用的组件（步骤4.2）
from .dummy_processor import DummyProcessor

# 迭代5步骤5.1：基础图像工具函数
# 这些函数将在步骤5.1.2-5.1.3中实现
try:
    from .lore_image_utils import (
        get_affine_transform,
        get_affine_transform_upper_left,
        affine_transform,
        gaussian_radius,
        draw_umich_gaussian,
        color_aug
    )
    _LORE_IMAGE_UTILS_AVAILABLE = True
except ImportError:
    _LORE_IMAGE_UTILS_AVAILABLE = False

# 迭代11：工具函数导出
# from .post_process import post_process
# from .oracle_utils import oracle_utils
# from .eval_utils import eval_utils

__all__ = [
    "DummyProcessor",
    # 后续迭代将添加：
    # "post_process",
    # "oracle_utils",
    # "eval_utils",
]

# 如果图像工具函数可用，添加到导出列表
if _LORE_IMAGE_UTILS_AVAILABLE:
    __all__.extend([
        "get_affine_transform",
        "get_affine_transform_upper_left",
        "affine_transform",
        "gaussian_radius",
        "draw_umich_gaussian",
        "color_aug"
    ])
